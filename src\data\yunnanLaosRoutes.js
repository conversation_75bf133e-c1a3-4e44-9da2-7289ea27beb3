/**
 * 云南-老挝路径数据模块
 * Yunnan-Laos Route Data Module
 * 
 * 包含云南省与老挝之间的地理位置数据、路径连接信息和相关元数据
 * Contains geographic location data, route connection information and related metadata between Yunnan Province and Laos
 * 
 * 地理坐标系统说明 (Geographic Coordinate System Description):
 * - 使用WGS84坐标系统 (Uses WGS84 coordinate system)
 * - 坐标格式: [纬度, 经度] (Coordinate format: [latitude, longitude])
 * - 投影方式: EPSG:4326 (Projection: EPSG:4326)
 * - 精度: 小数点后4位 (Precision: 4 decimal places)
 * 
 * <AUTHOR> (Agricultural Development Project Team)
 * @version 1.0.0
 */

/**
 * 云南省主要城市和地点数据
 * Major cities and locations data in Yunnan Province
 */
export const yunnanLocations = [
  {
    id: 'kunming',
    name: '昆明市',
    nameEn: 'Kunming',
    coords: [25.0389, 102.7183], // 昆明市中心坐标 (Kunming city center coordinates)
    type: 'provincial_capital',
    country: 'China',
    province: 'Yunnan',
    description: '云南省省会，西南地区重要的中心城市 (Capital of Yunnan Province, important central city in Southwest China)',
    metadata: {
      population: 8460000,
      elevation: 1892, // 海拔米数 (Elevation in meters)
      economicZone: 'central_yunnan',
      majorIndustries: ['tourism', 'logistics', 'manufacturing', 'agriculture']
    }
  },
  {
    id: 'dali',
    name: '大理市',
    nameEn: 'Dali',
    coords: [25.6066, 100.2672],
    type: 'prefecture_city',
    country: 'China',
    province: 'Yunnan',
    description: '大理白族自治州州府，著名旅游城市 (Capital of Dali Bai Autonomous Prefecture, famous tourist city)',
    metadata: {
      population: 3500000,
      elevation: 1976,
      economicZone: 'western_yunnan',
      majorIndustries: ['tourism', 'agriculture', 'handicrafts']
    }
  },
  {
    id: 'lijiang',
    name: '丽江市',
    nameEn: 'Lijiang',
    coords: [26.8721, 100.2240],
    type: 'prefecture_city',
    country: 'China',
    province: 'Yunnan',
    description: '世界文化遗产城市，纳西族文化中心 (World Cultural Heritage city, center of Naxi culture)',
    metadata: {
      population: 1250000,
      elevation: 2418,
      economicZone: 'northwestern_yunnan',
      majorIndustries: ['tourism', 'culture', 'agriculture']
    }
  },
  {
    id: 'xishuangbanna',
    name: '西双版纳',
    nameEn: 'Xishuangbanna',
    coords: [22.0017, 100.7975],
    type: 'autonomous_prefecture',
    country: 'China',
    province: 'Yunnan',
    description: '西双版纳傣族自治州，热带雨林生态旅游区 (Xishuangbanna Dai Autonomous Prefecture, tropical rainforest eco-tourism area)',
    metadata: {
      population: 1300000,
      elevation: 552,
      economicZone: 'southern_yunnan',
      majorIndustries: ['tourism', 'tropical_agriculture', 'rubber', 'tea']
    }
  },
  {
    id: 'baoshan',
    name: '保山市',
    nameEn: 'Baoshan',
    coords: [25.1204, 99.1670],
    type: 'prefecture_city',
    country: 'China',
    province: 'Yunnan',
    description: '滇西重要的交通枢纽和商贸中心 (Important transportation hub and commercial center in western Yunnan)',
    metadata: {
      population: 2600000,
      elevation: 1654,
      economicZone: 'western_yunnan',
      majorIndustries: ['logistics', 'agriculture', 'mining', 'trade']
    }
  },
  {
    id: 'dehong',
    name: '德宏州',
    nameEn: 'Dehong',
    coords: [24.4367, 98.5784],
    type: 'autonomous_prefecture',
    country: 'China',
    province: 'Yunnan',
    description: '德宏傣族景颇族自治州，中缅边境重要口岸 (Dehong Dai and Jingpo Autonomous Prefecture, important China-Myanmar border port)',
    metadata: {
      population: 1310000,
      elevation: 927,
      economicZone: 'western_yunnan',
      majorIndustries: ['border_trade', 'agriculture', 'tourism']
    }
  }
]

/**
 * 老挝主要城市和地点数据
 * Major cities and locations data in Laos
 */
export const laosLocations = [
  {
    id: 'vientiane',
    name: '万象',
    nameEn: 'Vientiane',
    coords: [17.9757, 102.6331],
    type: 'capital',
    country: 'Laos',
    province: 'Vientiane',
    description: '老挝人民民主共和国首都和最大城市 (Capital and largest city of Lao People\'s Democratic Republic)',
    metadata: {
      population: 948477,
      elevation: 174,
      economicZone: 'central_laos',
      majorIndustries: ['government', 'services', 'manufacturing', 'tourism']
    }
  },
  {
    id: 'luang_prabang',
    name: '琅勃拉邦',
    nameEn: 'Luang Prabang',
    coords: [19.8845, 102.1348],
    type: 'provincial_capital',
    country: 'Laos',
    province: 'Luang Prabang',
    description: '联合国世界文化遗产城市，老挝古都 (UNESCO World Heritage city, ancient capital of Laos)',
    metadata: {
      population: 66781,
      elevation: 305,
      economicZone: 'northern_laos',
      majorIndustries: ['tourism', 'handicrafts', 'agriculture']
    }
  },
  {
    id: 'pakse',
    name: '巴色',
    nameEn: 'Pakse',
    coords: [15.1202, 105.7985],
    type: 'provincial_capital',
    country: 'Laos',
    province: 'Champasak',
    description: '老挝南部最大城市，湄公河重要港口 (Largest city in southern Laos, important Mekong River port)',
    metadata: {
      population: 87000,
      elevation: 107,
      economicZone: 'southern_laos',
      majorIndustries: ['agriculture', 'trade', 'tourism', 'fishing']
    }
  },
  {
    id: 'savannakhet',
    name: '沙湾拿吉',
    nameEn: 'Savannakhet',
    coords: [16.5563, 104.7573],
    type: 'provincial_capital',
    country: 'Laos',
    province: 'Savannakhet',
    description: '老挝第二大城市，重要的贸易和交通枢纽 (Second largest city in Laos, important trade and transportation hub)',
    metadata: {
      population: 125760,
      elevation: 156,
      economicZone: 'central_laos',
      majorIndustries: ['trade', 'agriculture', 'manufacturing', 'logistics']
    }
  },
  {
    id: 'thakhek',
    name: '他曲',
    nameEn: 'Thakhek',
    coords: [17.4081, 104.7990],
    type: 'provincial_capital',
    country: 'Laos',
    province: 'Khammouane',
    description: '甘蒙省省会，湄公河沿岸重要城市 (Capital of Khammouane Province, important city along Mekong River)',
    metadata: {
      population: 85000,
      elevation: 151,
      economicZone: 'central_laos',
      majorIndustries: ['agriculture', 'trade', 'tourism']
    }
  }
]

/**
 * 云南-老挝主要路径连接数据
 * Major route connections data between Yunnan and Laos
 */
export const yunnanLaosRoutes = [
  {
    id: 'kunming-vientiane-main',
    sourceId: 'kunming',
    targetId: 'vientiane',
    coords: [[25.0389, 102.7183], [17.9757, 102.6331]],
    value: 320,
    name: '昆明-万象主干线 (Kunming-Vientiane Main Line)',
    type: 'trade_corridor',
    description: '中老铁路主要贸易通道，连接中国西南与东南亚 (China-Laos Railway main trade corridor, connecting Southwest China with Southeast Asia)',
    metadata: {
      distance: 785, // 公里 (kilometers)
      transportMode: 'railway',
      frequency: 'daily',
      cargoTypes: ['manufactured_goods', 'agricultural_products', 'raw_materials'],
      establishedYear: 2021,
      strategicImportance: 'high'
    }
  },
  {
    id: 'xishuangbanna-luang_prabang',
    sourceId: 'xishuangbanna',
    targetId: 'luang_prabang',
    coords: [[22.0017, 100.7975], [19.8845, 102.1348]],
    value: 180,
    name: '西双版纳-琅勃拉邦旅游走廊 (Xishuangbanna-Luang Prabang Tourism Corridor)',
    type: 'tourism_route',
    description: '跨境旅游黄金线路，连接两个世界文化遗产地区 (Cross-border golden tourism route, connecting two World Heritage areas)',
    metadata: {
      distance: 420,
      transportMode: 'highway',
      frequency: 'weekly',
      cargoTypes: ['tourists', 'cultural_products', 'food'],
      establishedYear: 2018,
      strategicImportance: 'medium'
    }
  },
  {
    id: 'dali-vientiane-logistics',
    sourceId: 'dali',
    targetId: 'vientiane',
    coords: [[25.6066, 100.2672], [17.9757, 102.6331]],
    value: 240,
    name: '大理-万象物流通道 (Dali-Vientiane Logistics Corridor)',
    type: 'logistics_route',
    description: '滇西地区重要的货物运输通道 (Important cargo transportation corridor in western Yunnan)',
    metadata: {
      distance: 920,
      transportMode: 'highway',
      frequency: 'bi-weekly',
      cargoTypes: ['agricultural_products', 'textiles', 'machinery'],
      establishedYear: 2019,
      strategicImportance: 'medium'
    }
  },
  {
    id: 'baoshan-savannakhet-trade',
    sourceId: 'baoshan',
    targetId: 'savannakhet',
    coords: [[25.1204, 99.1670], [16.5563, 104.7573]],
    value: 150,
    name: '保山-沙湾拿吉贸易线 (Baoshan-Savannakhet Trade Route)',
    type: 'border_trade',
    description: '中老边境贸易重要通道，促进双边经济合作 (Important China-Laos border trade corridor, promoting bilateral economic cooperation)',
    metadata: {
      distance: 1150,
      transportMode: 'highway',
      frequency: 'monthly',
      cargoTypes: ['minerals', 'agricultural_products', 'consumer_goods'],
      establishedYear: 2020,
      strategicImportance: 'medium'
    }
  },
  {
    id: 'lijiang-pakse-cultural',
    sourceId: 'lijiang',
    targetId: 'pakse',
    coords: [[26.8721, 100.2240], [15.1202, 105.7985]],
    value: 95,
    name: '丽江-巴色文化交流线 (Lijiang-Pakse Cultural Exchange Route)',
    type: 'cultural_exchange',
    description: '促进中老文化交流与合作的重要纽带 (Important link promoting China-Laos cultural exchange and cooperation)',
    metadata: {
      distance: 1380,
      transportMode: 'air',
      frequency: 'quarterly',
      cargoTypes: ['cultural_products', 'educational_materials', 'art'],
      establishedYear: 2022,
      strategicImportance: 'low'
    }
  },
  {
    id: 'dehong-thakhek-border',
    sourceId: 'dehong',
    targetId: 'thakhek',
    coords: [[24.4367, 98.5784], [17.4081, 104.7990]],
    value: 110,
    name: '德宏-他曲边境通道 (Dehong-Thakhek Border Corridor)',
    type: 'border_crossing',
    description: '中老边境重要的人员和货物通道 (Important China-Laos border corridor for people and goods)',
    metadata: {
      distance: 890,
      transportMode: 'highway',
      frequency: 'weekly',
      cargoTypes: ['border_trade_goods', 'agricultural_products'],
      establishedYear: 2017,
      strategicImportance: 'medium'
    }
  }
]

/**
 * 区域地图配置参数
 * Regional map configuration parameters
 */
export const yunnanLaosMapConfig = {
  // 地图中心点坐标 (Map center coordinates)
  center: [21.5, 101.5], // 云南-老挝区域中心点 (Center point of Yunnan-Laos region)
  
  // 缩放级别 (Zoom level)
  zoom: 6, // 区域级别缩放，显示详细地理信息 (Regional level zoom, showing detailed geographic information)
  
  // 地理边界 (Geographic boundaries)
  bounds: {
    north: 29.0, // 北纬29度 (29°N)
    south: 14.0, // 北纬14度 (14°N)
    east: 108.0, // 东经108度 (108°E)
    west: 97.0   // 东经97度 (97°E)
  },
  
  // 地图投影设置 (Map projection settings)
  projection: {
    type: 'EPSG:4326', // WGS84地理坐标系 (WGS84 geographic coordinate system)
    units: 'degrees'   // 度为单位 (Degrees as units)
  },
  
  // 显示设置 (Display settings)
  display: {
    showCountryBorders: true,    // 显示国界线 (Show country borders)
    showProvinceBorders: true,   // 显示省界线 (Show province borders)
    showMajorCities: true,       // 显示主要城市 (Show major cities)
    showRivers: true,            // 显示主要河流 (Show major rivers)
    showMountains: false,        // 不显示山脉 (Don't show mountains)
    showRoads: true              // 显示主要道路 (Show major roads)
  },
  
  // 样式配置 (Style configuration)
  styles: {
    backgroundColor: '#122e2c',   // 背景色 (Background color)
    landColor: '#1e4133',        // 陆地颜色 (Land color)
    waterColor: '#122e2c',       // 水体颜色 (Water color)
    borderColor: '#4c9f7b',      // 边界线颜色 (Border color)
    cityColor: '#00ffcc',        // 城市标记颜色 (City marker color)
    routeColor: '#ffffff'        // 路径线颜色 (Route line color)
  }
}

/**
 * 获取所有位置数据
 * Get all location data
 * @returns {Array} 合并的位置数据数组 (Combined location data array)
 */
export function getAllLocations() {
  return [...yunnanLocations, ...laosLocations]
}

/**
 * 根据ID获取位置信息
 * Get location information by ID
 * @param {string} locationId - 位置ID (Location ID)
 * @returns {Object|null} 位置信息对象或null (Location information object or null)
 */
export function getLocationById(locationId) {
  const allLocations = getAllLocations()
  return allLocations.find(location => location.id === locationId) || null
}

/**
 * 根据类型过滤位置
 * Filter locations by type
 * @param {string} type - 位置类型 (Location type)
 * @returns {Array} 过滤后的位置数组 (Filtered location array)
 */
export function getLocationsByType(type) {
  const allLocations = getAllLocations()
  return allLocations.filter(location => location.type === type)
}

/**
 * 根据国家过滤位置
 * Filter locations by country
 * @param {string} country - 国家名称 (Country name)
 * @returns {Array} 过滤后的位置数组 (Filtered location array)
 */
export function getLocationsByCountry(country) {
  const allLocations = getAllLocations()
  return allLocations.filter(location => location.country === country)
}

/**
 * 计算两点间距离（简化版本）
 * Calculate distance between two points (simplified version)
 * @param {Array} coord1 - 第一个坐标点 [lat, lng] (First coordinate point)
 * @param {Array} coord2 - 第二个坐标点 [lat, lng] (Second coordinate point)
 * @returns {number} 距离（公里） (Distance in kilometers)
 */
export function calculateDistance(coord1, coord2) {
  const [lat1, lng1] = coord1
  const [lat2, lng2] = coord2
  
  const R = 6371 // 地球半径（公里） (Earth radius in kilometers)
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// 默认导出配置对象 (Default export configuration object)
export default {
  yunnanLocations,
  laosLocations,
  yunnanLaosRoutes,
  yunnanLaosMapConfig,
  getAllLocations,
  getLocationById,
  getLocationsByType,
  getLocationsByCountry,
  calculateDistance
}
