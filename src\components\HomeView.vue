<template>
  <div class="yunnan-laos-home">
    <!-- 页面标题 (Page Title) -->
    <header class="page-header">
      <h1>中老路线可视化 (Yunnan-Laos Trade Route Visualization)</h1>
      <p>动态数据流可视化平台 (Dynamic Data Flow Visualization Platform)</p>
    </header>

    <!-- 地图容器 (Map Container) -->
    <div class="map-container">
      <standalone-world-map ref="worldMap" :path-data="pathData" :height="mapHeight"
        :animation-enabled="animationEnabled" :flow-animation-speed="flowSpeed" :flow-particle-count="particleCount"
        :regional-mode="true" :region-config="regionConfig" :colors="mapColors" :show-title="false"
        :minimalist-mode="minimalistMode" :show-grid="false" @map-ready="onMapReady" />
    </div>

    <!-- 控制面板 (Control Panel) -->
    <div class="control-panel">
      <div class="control-group">
        <h3>动画控制 (Animation Controls)</h3>
        <div class="control-row">
          <label>动画开关 (Animation):</label>
          <button @click="toggleAnimation" class="btn btn-toggle">
            {{ animationEnabled ? '关闭' : '开启' }} ({{ animationEnabled ? 'OFF' : 'ON' }})
          </button>
        </div>
        <div class="control-row">
          <label>地图模式 (Map Mode):</label>
          <button @click="toggleMinimalistMode" class="btn btn-toggle">
            {{ minimalistMode ? '标准' : '简洁' }} ({{ minimalistMode ? 'Standard' : 'Clean' }})
          </button>
        </div>
        <div class="control-row">
          <label>流动速度 (Flow Speed):</label>
          <input type="range" v-model="flowSpeed" min="0.002" max="0.02" step="0.002" class="slider" />
          <span>{{ flowSpeed }}</span>
        </div>
        <div class="control-row">
          <label>粒子数量 (Particles):</label>
          <input type="range" v-model="particleCount" min="1" max="8" step="1" class="slider" />
          <span>{{ particleCount }}</span>
        </div>
      </div>

      <!-- 状态信息 (Status Info) -->
      <div class="status-group">
        <div class="status-item">
          <span class="label">路径数量 (Routes):</span>
          <span class="value">{{ pathData.length }}</span>
        </div>
        <div class="status-item">
          <span class="label">地图模式 (Mode):</span>
          <span class="value">{{ minimalistMode ? '简洁模式' : '标准模式' }} ({{ minimalistMode ? 'Clean' : 'Standard'
            }})</span>
        </div>
        <div class="status-item">
          <span class="label">动画状态 (Animation):</span>
          <span class="value">{{ animationEnabled ? '开启' : '关闭' }} ({{ animationEnabled ? 'ON' : 'OFF' }})</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'
import { yunnanLaosRoutes, yunnanLaosMapConfig } from '@/data/yunnanLaosRoutes.js'

export default {
  name: 'HomeView',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      // 地图数据 (Map Data)
      pathData: [],

      // 动画控制 (Animation Controls)
      animationEnabled: true,
      flowSpeed: 0.008,
      particleCount: 3,

      // 地图显示模式 (Map Display Mode)
      minimalistMode: true,

      // 区域配置 (Regional Configuration)
      regionConfig: {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      },

      // 地图样式 (Map Colors)
      mapColors: {
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      }
    }
  },
  computed: {
    // 计算地图高度 (Calculate map height)
    mapHeight() {
      return 'calc(100vh - 180px)' // 减去标题和控制面板的高度 (Subtract header and control panel height)
    }
  },
  methods: {
    /**
     * 加载云南-老挝路径数据 (Load Yunnan-Laos route data)
     */
    loadYunnanLaosData() {
      console.log('加载云南-老挝路径数据 (Loading Yunnan-Laos route data)...')

      // 转换数据格式为组件所需格式 (Convert data format for component)
      this.pathData = yunnanLaosRoutes.map(route => ({
        id: route.id,
        coords: route.coords,
        value: route.value,
        name: route.name,
        type: route.type,
        metadata: route.metadata
      }))

      console.log(`云南-老挝数据加载完成 (Yunnan-Laos data loaded): ${this.pathData.length} 条路径 (routes)`)
    },

    /**
     * 切换动画状态 (Toggle animation state)
     */
    toggleAnimation() {
      this.animationEnabled = !this.animationEnabled
      console.log(`动画${this.animationEnabled ? '开启' : '关闭'} (Animation ${this.animationEnabled ? 'enabled' : 'disabled'})`)
    },

    /**
     * 切换地图显示模式 (Toggle map display mode)
     */
    toggleMinimalistMode() {
      this.minimalistMode = !this.minimalistMode
      console.log(`地图模式切换为${this.minimalistMode ? '简洁' : '标准'}模式 (Map mode switched to ${this.minimalistMode ? 'minimalist' : 'standard'} mode)`)
    },

    /**
     * 地图就绪回调 (Map ready callback)
     */
    onMapReady(map) {
      console.log('云南-老挝区域地图就绪 (Yunnan-Laos regional map ready):', map)
      console.log('地图中心点 (Map center):', map.getCenter())
      console.log('地图缩放级别 (Map zoom level):', map.getZoom())
    }
  },

  /**
   * 组件挂载后自动加载数据 (Auto load data after component mounted)
   */
  mounted() {
    console.log('云南-老挝主页组件已挂载 (Yunnan-Laos home component mounted)')

    // 自动加载云南-老挝路径数据 (Auto load Yunnan-Laos route data)
    this.loadYunnanLaosData()
  }
}
</script>

<style scoped>
/* 云南-老挝主页样式 (Yunnan-Laos Home Page Styles) */
.yunnan-laos-home {
  width: 100vw;
  height: 100vh;
  background-color: #0F2E2C;
  color: #fff;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 页面标题样式 (Page Header Styles) */
.page-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, rgba(0, 255, 204, 0.1), rgba(0, 255, 204, 0.05));
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
  text-align: center;
  flex-shrink: 0;
}

.page-header h1 {
  color: #00FFCC;
  font-size: 24px;
  margin: 0 0 5px 0;
  font-weight: 600;
}

.page-header p {
  color: #ccc;
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

/* 地图容器样式 (Map Container Styles) */
.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 控制面板样式 (Control Panel Styles) */
.control-panel {
  padding: 15px 20px;
  background-color: rgba(0, 0, 0, 0.4);
  border-top: 1px solid rgba(0, 255, 204, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  flex-shrink: 0;
}

/* 控制组样式 (Control Group Styles) */
.control-group {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-group h3 {
  color: #00FFCC;
  margin: 0;
  font-size: 16px;
  white-space: nowrap;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 10px;
  white-space: nowrap;
}

.control-row label {
  color: #ccc;
  font-size: 14px;
  min-width: 120px;
}

/* 按钮样式 (Button Styles) */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 60px;
}

.btn-toggle {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.btn-toggle:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

/* 滑块样式 (Slider Styles) */
.slider {
  width: 100px;
  height: 4px;
  background: rgba(0, 255, 204, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #00FFCC;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #00FFCC;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.control-row span {
  color: #00FFCC;
  font-size: 12px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

/* 状态组样式 (Status Group Styles) */
.status-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 255, 204, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(0, 255, 204, 0.2);
  min-width: 100px;
}

.status-item .label {
  color: #ccc;
  font-size: 11px;
  margin-bottom: 2px;
}

.status-item .value {
  color: #00FFCC;
  font-size: 13px;
  font-weight: 600;
}

/* 科技网格背景 (Tech Grid Background) */
.yunnan-laos-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: 0;
}

/* 响应式设计 (Responsive Design) */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 20px;
  }

  .page-header p {
    font-size: 12px;
  }

  .control-panel {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .control-group {
    justify-content: center;
  }

  .status-group {
    justify-content: center;
  }

  .control-row {
    justify-content: space-between;
  }

  .control-row label {
    min-width: 100px;
  }

  .slider {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 10px 15px;
  }

  .control-panel {
    padding: 10px 15px;
  }

  .control-group {
    flex-direction: column;
    gap: 10px;
  }

  .control-row {
    width: 100%;
  }
}
</style>
