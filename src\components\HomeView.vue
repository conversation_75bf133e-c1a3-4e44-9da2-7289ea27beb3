<template>
  <div class="yunnan-laos-home">
    <!-- 地图容器 (Map Container) -->
    <standalone-world-map ref="worldMap" :path-data="pathData" :height="'100vh'" :animation-enabled="true"
      :flow-animation-speed="0.008" :flow-particle-count="3" :regional-mode="true" :region-config="regionConfig"
      :colors="mapColors" :show-title="false" :minimalist-mode="true" :show-grid="false" @map-ready="onMapReady" />

    <!-- 颜色控制触发按钮 (Color Control Trigger Button) -->
    <button @click="toggleColorControls" class="color-control-trigger" :class="{ active: showColorControls }"
      title="颜色设置 (Color Settings)">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path
          d="M12,18.5A1.5,1.5 0 0,1 10.5,17A1.5,1.5 0 0,1 12,15.5A1.5,1.5 0 0,1 13.5,17A1.5,1.5 0 0,1 12,18.5M21.71,11.29L12.71,2.29A1,1 0 0,0 12,2A1,1 0 0,0 11.29,2.29L2.29,11.29A1,1 0 0,0 2.29,12.71L11.29,21.71A1,1 0 0,0 12,22A1,1 0 0,0 12.71,21.71L21.71,12.71A1,1 0 0,0 21.71,11.29M7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12Z" />
      </svg>
    </button>

    <!-- 颜色控制面板 (Color Control Panel) -->
    <div class="color-control-panel" :class="{ visible: showColorControls }">
      <div class="panel-header">
        <h3>颜色设置 (Color Settings)</h3>
        <button @click="toggleColorControls" class="close-btn" title="关闭 (Close)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </button>
      </div>

      <!-- 预设颜色方案 (Preset Color Schemes) -->
      <div class="preset-section">
        <h4>预设方案 (Presets)</h4>
        <div class="preset-buttons">
          <button v-for="(preset, key) in colorPresets" :key="key" @click="applyColorPreset(key)" class="preset-btn"
            :title="preset.name">
            <div class="preset-preview">
              <div class="land-preview" :style="{ backgroundColor: preset.land }"></div>
              <div class="ocean-preview" :style="{ backgroundColor: preset.ocean }"></div>
            </div>
            <span>{{ preset.name.split(' ')[0] }}</span>
          </button>
        </div>
      </div>

      <!-- 自定义颜色控制 (Custom Color Controls) -->
      <div class="custom-section">
        <h4>自定义颜色 (Custom Colors)</h4>

        <div class="color-control">
          <label>陆地颜色 (Land Color):</label>
          <div class="color-input-group">
            <input type="color" :value="rgbToHex(mapColors.land)"
              @input="updateLandColor(hexToRgb($event.target.value))" class="color-picker" />
            <span class="color-value">{{ mapColors.land }}</span>
          </div>
        </div>

        <div class="color-control">
          <label>海洋颜色 (Ocean Color):</label>
          <div class="color-input-group">
            <input type="color" :value="rgbToHex(mapColors.ocean)"
              @input="updateOceanColor(hexToRgb($event.target.value))" class="color-picker" />
            <span class="color-value">{{ mapColors.ocean }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 (Action Buttons) -->
      <div class="action-section">
        <button @click="resetToDefaultColors" class="reset-btn">
          重置默认 (Reset Default)
        </button>
      </div>
    </div>

    <!-- 背景遮罩 (Background Overlay) -->
    <div v-if="showColorControls" class="panel-overlay" @click="toggleColorControls"></div>
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'
import { yunnanLaosRoutes, yunnanLaosMapConfig } from '@/data/yunnanLaosRoutes.js'

export default {
  name: 'HomeView',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      // 地图数据 (Map Data)
      pathData: [],

      // 区域配置 (Regional Configuration)
      regionConfig: {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      },

      // 颜色控制面板状态 (Color Control Panel State)
      showColorControls: false,

      // 地图样式 (Map Colors) - 更亮的默认颜色 (Brighter default colors)
      mapColors: {
        land: 'rgb(45, 85, 65)',      // 更亮的陆地颜色 (Brighter land color)
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(28, 58, 55)',     // 更亮的海洋颜色 (Brighter ocean color)
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      },

      // 预设颜色方案 (Preset Color Schemes)
      colorPresets: {
        darkTech: {
          name: '深色科技 (Dark Tech)',
          land: 'rgb(45, 85, 65)',
          ocean: 'rgb(28, 58, 55)'
        },
        lightMode: {
          name: '浅色模式 (Light Mode)',
          land: 'rgb(180, 200, 180)',
          ocean: 'rgb(120, 150, 180)'
        },
        highContrast: {
          name: '高对比度 (High Contrast)',
          land: 'rgb(60, 100, 80)',
          ocean: 'rgb(20, 40, 60)'
        },
        midnight: {
          name: '午夜蓝 (Midnight Blue)',
          land: 'rgb(40, 60, 80)',
          ocean: 'rgb(20, 30, 50)'
        },
        forest: {
          name: '森林绿 (Forest Green)',
          land: 'rgb(60, 100, 60)',
          ocean: 'rgb(30, 50, 70)'
        }
      }
    }
  },
  methods: {
    /**
     * 加载云南-老挝路径数据 (Load Yunnan-Laos route data)
     */
    loadYunnanLaosData() {
      console.log('加载云南-老挝路径数据 (Loading Yunnan-Laos route data)...')

      // 转换数据格式为组件所需格式 (Convert data format for component)
      this.pathData = yunnanLaosRoutes.map(route => ({
        id: route.id,
        coords: route.coords,
        value: route.value,
        name: route.name,
        type: route.type,
        metadata: route.metadata
      }))

      console.log(`云南-老挝数据加载完成 (Yunnan-Laos data loaded): ${this.pathData.length} 条路径 (routes)`)
    },



    /**
     * 切换颜色控制面板显示状态 (Toggle color control panel visibility)
     */
    toggleColorControls() {
      this.showColorControls = !this.showColorControls
      console.log(`颜色控制面板${this.showColorControls ? '打开' : '关闭'} (Color control panel ${this.showColorControls ? 'opened' : 'closed'})`)
    },

    /**
     * 应用预设颜色方案 (Apply preset color scheme)
     * @param {string} presetKey - 预设方案键名 (Preset scheme key)
     */
    applyColorPreset(presetKey) {
      const preset = this.colorPresets[presetKey]
      if (preset) {
        this.mapColors.land = preset.land
        this.mapColors.ocean = preset.ocean
        this.saveColorPreferences()
        console.log(`应用颜色预设: ${preset.name} (Applied color preset: ${preset.name})`)
      }
    },

    /**
     * 更新陆地颜色 (Update land color)
     * @param {string} color - 新的陆地颜色 (New land color)
     */
    updateLandColor(color) {
      this.mapColors.land = color
      this.saveColorPreferences()
      console.log(`陆地颜色更新为: ${color} (Land color updated to: ${color})`)
    },

    /**
     * 更新海洋颜色 (Update ocean color)
     * @param {string} color - 新的海洋颜色 (New ocean color)
     */
    updateOceanColor(color) {
      this.mapColors.ocean = color
      this.saveColorPreferences()
      console.log(`海洋颜色更新为: ${color} (Ocean color updated to: ${color})`)
    },

    /**
     * 保存颜色偏好设置到本地存储 (Save color preferences to local storage)
     */
    saveColorPreferences() {
      try {
        const colorPrefs = {
          land: this.mapColors.land,
          ocean: this.mapColors.ocean,
          timestamp: new Date().toISOString()
        }
        localStorage.setItem('yunnan-laos-map-colors', JSON.stringify(colorPrefs))
        console.log('颜色偏好已保存 (Color preferences saved)')
      } catch (error) {
        console.warn('保存颜色偏好失败 (Failed to save color preferences):', error)
      }
    },

    /**
     * 从本地存储加载颜色偏好设置 (Load color preferences from local storage)
     */
    loadColorPreferences() {
      try {
        const saved = localStorage.getItem('yunnan-laos-map-colors')
        if (saved) {
          const colorPrefs = JSON.parse(saved)
          this.mapColors.land = colorPrefs.land || this.mapColors.land
          this.mapColors.ocean = colorPrefs.ocean || this.mapColors.ocean
          console.log('颜色偏好已加载 (Color preferences loaded):', colorPrefs)
        }
      } catch (error) {
        console.warn('加载颜色偏好失败 (Failed to load color preferences):', error)
      }
    },

    /**
     * 重置为默认颜色 (Reset to default colors)
     */
    resetToDefaultColors() {
      this.mapColors.land = this.colorPresets.darkTech.land
      this.mapColors.ocean = this.colorPresets.darkTech.ocean
      this.saveColorPreferences()
      console.log('颜色已重置为默认值 (Colors reset to default)')
    },

    /**
     * RGB颜色转换为十六进制 (Convert RGB color to hex)
     * @param {string} rgb - RGB颜色字符串 (RGB color string)
     * @returns {string} 十六进制颜色 (Hex color)
     */
    rgbToHex(rgb) {
      const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
      if (!match) return '#000000'

      const r = parseInt(match[1])
      const g = parseInt(match[2])
      const b = parseInt(match[3])

      return '#' + [r, g, b].map(x => {
        const hex = x.toString(16)
        return hex.length === 1 ? '0' + hex : hex
      }).join('')
    },

    /**
     * 十六进制颜色转换为RGB (Convert hex color to RGB)
     * @param {string} hex - 十六进制颜色 (Hex color)
     * @returns {string} RGB颜色字符串 (RGB color string)
     */
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      if (!result) return 'rgb(0, 0, 0)'

      const r = parseInt(result[1], 16)
      const g = parseInt(result[2], 16)
      const b = parseInt(result[3], 16)

      return `rgb(${r}, ${g}, ${b})`
    },

    /**
     * 地图就绪回调 (Map ready callback)
     */
    onMapReady(map) {
      console.log('云南-老挝区域地图就绪 (Yunnan-Laos regional map ready):', map)
      console.log('地图中心点 (Map center):', map.getCenter())
      console.log('地图缩放级别 (Map zoom level):', map.getZoom())
    }
  },

  /**
   * 组件挂载后自动加载数据 (Auto load data after component mounted)
   */
  mounted() {
    console.log('云南-老挝主页组件已挂载 (Yunnan-Laos home component mounted)')

    // 加载保存的颜色偏好设置 (Load saved color preferences)
    this.loadColorPreferences()

    // 自动加载云南-老挝路径数据 (Auto load Yunnan-Laos route data)
    this.loadYunnanLaosData()
  }
}
</script>

<style scoped>
/* 云南-老挝主页样式 - 全屏地图模式 (Yunnan-Laos Home Page Styles - Full Screen Map Mode) */
.yunnan-laos-home {
  width: 100vw;
  height: 100vh;
  background-color: #0F2E2C;
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

/* 科技网格背景 (Tech Grid Background) */
.yunnan-laos-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: 0;
}

/* 颜色控制触发按钮 (Color Control Trigger Button) */
.color-control-trigger {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(0, 255, 204, 0.1);
  border: 2px solid rgba(0, 255, 204, 0.3);
  border-radius: 50%;
  color: #00FFCC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.color-control-trigger:hover {
  background: rgba(0, 255, 204, 0.2);
  border-color: rgba(0, 255, 204, 0.5);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 255, 204, 0.3);
}

.color-control-trigger.active {
  background: rgba(0, 255, 204, 0.3);
  border-color: #00FFCC;
  transform: scale(0.95);
}

.color-control-trigger svg {
  transition: transform 0.3s ease;
}

.color-control-trigger.active svg {
  transform: rotate(180deg);
}

/* 颜色控制面板 (Color Control Panel) */
.color-control-panel {
  position: fixed;
  top: 20px;
  right: 80px;
  width: 320px;
  max-height: calc(100vh - 40px);
  background: rgba(15, 46, 44, 0.95);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 999;
  transform: translateX(100%) scale(0.8);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.color-control-panel.visible {
  transform: translateX(0) scale(1);
  opacity: 1;
}

/* 面板头部 (Panel Header) */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 255, 204, 0.2);
  background: rgba(0, 255, 204, 0.05);
}

.panel-header h3 {
  color: #00FFCC;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #00FFCC;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(0, 255, 204, 0.1);
  transform: scale(1.1);
}

/* 面板内容区域 (Panel Content Areas) */
.preset-section,
.custom-section,
.action-section {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 255, 204, 0.1);
}

.action-section {
  border-bottom: none;
}

.preset-section h4,
.custom-section h4 {
  color: #00FFCC;
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 500;
}

/* 预设按钮 (Preset Buttons) */
.preset-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.preset-btn {
  background: rgba(0, 255, 204, 0.05);
  border: 1px solid rgba(0, 255, 204, 0.2);
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #ccc;
  font-size: 12px;
}

.preset-btn:hover {
  background: rgba(0, 255, 204, 0.1);
  border-color: rgba(0, 255, 204, 0.4);
  transform: translateY(-2px);
}

.preset-preview {
  display: flex;
  width: 40px;
  height: 20px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.land-preview,
.ocean-preview {
  flex: 1;
  height: 100%;
}

/* 自定义颜色控制 (Custom Color Controls) */
.color-control {
  margin-bottom: 15px;
}

.color-control:last-child {
  margin-bottom: 0;
}

.color-control label {
  display: block;
  color: #ccc;
  font-size: 13px;
  margin-bottom: 8px;
  font-weight: 500;
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-picker {
  width: 40px;
  height: 30px;
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 6px;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-picker:hover {
  border-color: rgba(0, 255, 204, 0.5);
  transform: scale(1.05);
}

.color-value {
  flex: 1;
  color: #00FFCC;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  background: rgba(0, 255, 204, 0.05);
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

/* 重置按钮 (Reset Button) */
.reset-btn {
  width: 100%;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 6px;
  color: #ff6b6b;
  padding: 10px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.5);
  transform: translateY(-1px);
}

/* 背景遮罩 (Background Overlay) */
.panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

/* 响应式设计 (Responsive Design) */
@media (max-width: 768px) {
  .color-control-trigger {
    top: 15px;
    right: 15px;
    width: 45px;
    height: 45px;
  }

  .color-control-panel {
    top: 15px;
    right: 15px;
    left: 15px;
    width: auto;
    max-height: calc(100vh - 30px);
  }

  .color-control-panel.visible {
    transform: translateY(0) scale(1);
  }

  .color-control-panel {
    transform: translateY(-100%) scale(0.9);
  }

  .preset-buttons {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .preset-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .color-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .color-picker {
    width: 100%;
    height: 35px;
  }
}

/* 滚动条样式 (Scrollbar Styling) */
.color-control-panel::-webkit-scrollbar {
  width: 6px;
}

.color-control-panel::-webkit-scrollbar-track {
  background: rgba(0, 255, 204, 0.1);
  border-radius: 3px;
}

.color-control-panel::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 204, 0.3);
  border-radius: 3px;
}

.color-control-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 204, 0.5);
}
</style>
