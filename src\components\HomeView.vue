<template>
  <div class="yunnan-laos-home">
    <!-- 地图容器 (Map Container) -->
    <standalone-world-map ref="worldMap" :path-data="pathData" :height="'100vh'" :animation-enabled="true"
      :flow-animation-speed="0.008" :flow-particle-count="3" :regional-mode="true" :region-config="regionConfig"
      :colors="mapColors" :show-title="false" :minimalist-mode="true" :show-grid="false" @map-ready="onMapReady" />
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'
import { yunnanLaosRoutes, yunnanLaosMapConfig } from '@/data/yunnanLaosRoutes.js'

export default {
  name: 'HomeView',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      // 地图数据 (Map Data)
      pathData: [],

      // 区域配置 (Regional Configuration)
      regionConfig: {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      },

      // 地图样式 (Map Colors)
      mapColors: {
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      }
    }
  },
  methods: {
    /**
     * 加载云南-老挝路径数据 (Load Yunnan-Laos route data)
     */
    loadYunnanLaosData() {
      console.log('加载云南-老挝路径数据 (Loading Yunnan-Laos route data)...')

      // 转换数据格式为组件所需格式 (Convert data format for component)
      this.pathData = yunnanLaosRoutes.map(route => ({
        id: route.id,
        coords: route.coords,
        value: route.value,
        name: route.name,
        type: route.type,
        metadata: route.metadata
      }))

      console.log(`云南-老挝数据加载完成 (Yunnan-Laos data loaded): ${this.pathData.length} 条路径 (routes)`)
    },



    /**
     * 地图就绪回调 (Map ready callback)
     */
    onMapReady(map) {
      console.log('云南-老挝区域地图就绪 (Yunnan-Laos regional map ready):', map)
      console.log('地图中心点 (Map center):', map.getCenter())
      console.log('地图缩放级别 (Map zoom level):', map.getZoom())
    }
  },

  /**
   * 组件挂载后自动加载数据 (Auto load data after component mounted)
   */
  mounted() {
    console.log('云南-老挝主页组件已挂载 (Yunnan-Laos home component mounted)')

    // 自动加载云南-老挝路径数据 (Auto load Yunnan-Laos route data)
    this.loadYunnanLaosData()
  }
}
</script>

<style scoped>
/* 云南-老挝主页样式 - 全屏地图模式 (Yunnan-Laos Home Page Styles - Full Screen Map Mode) */
.yunnan-laos-home {
  width: 100vw;
  height: 100vh;
  background-color: #0F2E2C;
  position: relative;
  overflow: hidden;
}

/* 科技网格背景 (Tech Grid Background) */
.yunnan-laos-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  pointer-events: none;
  z-index: 0;
}
</style>
