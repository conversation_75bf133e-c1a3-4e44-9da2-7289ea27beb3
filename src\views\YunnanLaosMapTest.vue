<template>
  <div class="yunnan-laos-map-test">
    <!-- 页面标题 (Page Header) -->
    <header class="test-header">
      <h1>云南-老挝区域地图测试 (Yunnan-Laos Regional Map Test)</h1>
      <p>动态数据源集成与区域聚焦地图可视化测试 (Dynamic Data Source Integration & Regional Focus Map Visualization Test)</p>
    </header>

    <div class="test-content">
      <!-- 控制面板 (Control Panel) -->
      <div class="controls-section">
        <h2>控制面板 (Control Panel)</h2>

        <!-- 数据源控制 (Data Source Controls) -->
        <div class="control-group">
          <h3>数据源管理 (Data Source Management)</h3>
          <div class="control-buttons">
            <button @click="loadApiData" class="btn btn-primary" :disabled="loading">
              {{ loading ? '加载中...' : '加载API数据' }} ({{ loading ? 'Loading...' : 'Load API Data' }})
            </button>
            <button @click="loadMockData" class="btn btn-secondary">
              加载模拟数据 (Load Mock Data)
            </button>
            <button @click="clearData" class="btn btn-warning">
              清除数据 (Clear Data)
            </button>
            <button @click="refreshData" class="btn btn-accent" :disabled="loading">
              刷新数据 (Refresh Data)
            </button>
          </div>
        </div>

        <!-- 地图视图控制 (Map View Controls) -->
        <div class="control-group">
          <h3>地图视图控制 (Map View Controls)</h3>
          <div class="control-buttons">
            <button @click="setRegionalView" class="btn btn-primary">
              区域视图 (Regional View)
            </button>
            <button @click="setGlobalView" class="btn btn-secondary">
              全球视图 (Global View)
            </button>
            <button @click="focusOnYunnan" class="btn btn-accent">
              聚焦云南 (Focus Yunnan)
            </button>
            <button @click="focusOnLaos" class="btn btn-accent">
              聚焦老挝 (Focus Laos)
            </button>
          </div>
        </div>

        <!-- 动画控制 (Animation Controls) -->
        <div class="animation-controls">
          <h3>动画控制 (Animation Controls)</h3>
          <div class="control-row">
            <label>动画开关 (Animation Toggle):</label>
            <button @click="toggleAnimation" class="btn btn-toggle">
              {{ animationEnabled ? '关闭动画' : '开启动画' }} ({{ animationEnabled ? 'Disable' : 'Enable' }})
            </button>
          </div>
          <div class="control-row">
            <label>流动速度 (Flow Speed):</label>
            <input type="range" v-model="flowSpeed" min="0.002" max="0.02" step="0.002" class="slider" />
            <span>{{ flowSpeed }}</span>
          </div>
          <div class="control-row">
            <label>粒子数量 (Particle Count):</label>
            <input type="range" v-model="particleCount" min="1" max="8" step="1" class="slider" />
            <span>{{ particleCount }}</span>
          </div>
        </div>

        <!-- 状态信息 (Status Information) -->
        <div class="status-info">
          <div class="status-item">
            <span class="label">数据源 (Data Source):</span>
            <span class="value">{{ dataSource }}</span>
          </div>
          <div class="status-item">
            <span class="label">路径数量 (Routes Count):</span>
            <span class="value">{{ pathData.length }}</span>
          </div>
          <div class="status-item">
            <span class="label">地图模式 (Map Mode):</span>
            <span class="value">{{ mapMode }}</span>
          </div>
          <div class="status-item">
            <span class="label">动画状态 (Animation):</span>
            <span class="value">{{ animationEnabled ? '开启' : '关闭' }} ({{ animationEnabled ? 'Enabled' : 'Disabled'
            }})</span>
          </div>
          <div class="status-item">
            <span class="label">地图就绪 (Map Ready):</span>
            <span class="value">{{ mapReady ? '是' : '否' }} ({{ mapReady ? 'Yes' : 'No' }})</span>
          </div>
        </div>
      </div>

      <!-- 地图容器 (Map Container) -->
      <div class="map-section">
        <h2>云南-老挝区域地图 (Yunnan-Laos Regional Map)</h2>
        <standalone-world-map ref="worldMap" :path-data="pathData" :height="600" :animation-enabled="animationEnabled"
          :flow-animation-speed="flowSpeed" :flow-particle-count="particleCount" :regional-mode="regionalMode"
          :region-config="regionConfig" :colors="mapColors" :title="mapTitle" @map-ready="onMapReady" />
      </div>

      <!-- 数据详情 (Data Details) -->
      <div class="data-details-section">
        <h2>数据详情 (Data Details)</h2>
        <div class="details-grid">
          <!-- 位置信息 (Location Information) -->
          <div class="detail-card">
            <h3>位置信息 (Locations)</h3>
            <div class="location-list">
              <div v-for="location in locationData" :key="location.id" class="location-item">
                <span class="location-name">{{ location.name }} ({{ location.nameEn }})</span>
                <span class="location-coords">[{{ location.coords[0].toFixed(4) }}, {{ location.coords[1].toFixed(4)
                }}]</span>
                <span class="location-type">{{ location.type }}</span>
              </div>
            </div>
          </div>

          <!-- 路径信息 (Route Information) -->
          <div class="detail-card">
            <h3>路径信息 (Routes)</h3>
            <div class="route-list">
              <div v-for="route in pathData" :key="route.id || route.name" class="route-item">
                <span class="route-name">{{ route.name }}</span>
                <span class="route-value">流量值 (Value): {{ route.value }}</span>
                <span class="route-type">类型 (Type): {{ route.type || 'trade' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 技术特性展示 (Technical Features) -->
      <div class="features-section">
        <h2>技术特性 (Technical Features)</h2>
        <div class="feature-grid">
          <div class="feature-item">
            <h3>✅ 动态数据源集成 (Dynamic Data Source Integration)</h3>
            <p>支持从API获取实时地理数据 (Supports fetching real-time geographic data from API)</p>
          </div>
          <div class="feature-item">
            <h3>✅ 区域聚焦显示 (Regional Focus Display)</h3>
            <p>云南-老挝区域专门优化的地图视图 (Map view specifically optimized for Yunnan-Laos region)</p>
          </div>
          <div class="feature-item">
            <h3>✅ 中文代码注释 (Chinese Code Comments)</h3>
            <p>完整的中文代码文档和注释 (Complete Chinese code documentation and comments)</p>
          </div>
          <div class="feature-item">
            <h3>✅ 地理坐标系统 (Geographic Coordinate System)</h3>
            <p>WGS84坐标系统，EPSG:4326投影 (WGS84 coordinate system, EPSG:4326 projection)</p>
          </div>
          <div class="feature-item">
            <h3>✅ 流动动画效果 (Flow Animation Effects)</h3>
            <p>沿路径的平滑粒子流动动画 (Smooth particle flow animation along paths)</p>
          </div>
          <div class="feature-item">
            <h3>✅ 可配置参数 (Configurable Parameters)</h3>
            <p>动画速度、粒子数量等可调节参数 (Adjustable parameters like animation speed, particle count)</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StandaloneWorldMap from '@/components/StandaloneWorldMap.vue'
import { MockApiDataGenerator } from '@/services/mapDataApi.js'
import {
  yunnanLaosRoutes,
  yunnanLaosMapConfig,
  getAllLocations
} from '@/data/yunnanLaosRoutes.js'

export default {
  name: 'YunnanLaosMapTest',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      // 地图数据 (Map Data)
      pathData: [],
      locationData: [],

      // 地图状态 (Map State)
      mapReady: false,
      loading: false,
      dataSource: 'none',
      mapMode: 'regional',

      // 动画控制 (Animation Controls)
      animationEnabled: true,
      flowSpeed: 0.008,
      particleCount: 3,

      // 区域模式配置 (Regional Mode Configuration)
      regionalMode: true,
      regionConfig: {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      },

      // 地图样式配置 (Map Style Configuration)
      mapColors: {
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      }
    }
  },
  computed: {
    mapTitle() {
      return this.regionalMode
        ? '云南-老挝区域贸易路线 (Yunnan-Laos Regional Trade Routes)'
        : '全球贸易路线 (Global Trade Routes)'
    }
  },
  methods: {
    /**
     * 加载API数据 (Load API Data)
     * 从模拟API获取云南-老挝路径数据
     */
    async loadApiData() {
      this.loading = true
      this.dataSource = 'loading'

      try {
        console.log('开始加载API数据 (Starting to load API data)...')

        // 调用模拟API获取数据 (Call mock API to get data)
        const response = await MockApiDataGenerator.generateYunnanLaosRoutes()

        if (response.success) {
          // 转换API数据格式为组件所需格式 (Convert API data format to component required format)
          this.pathData = response.data.routes.map(route => ({
            id: route.id,
            coords: route.coords,
            value: route.value,
            name: route.name,
            type: route.type,
            metadata: route.metadata
          }))

          this.locationData = response.data.locations
          this.dataSource = 'api'

          console.log(`API数据加载成功 (API data loaded successfully): ${this.pathData.length} 条路径 (routes)`)
        } else {
          throw new Error(response.message || 'API响应失败 (API response failed)')
        }
      } catch (error) {
        console.error('加载API数据失败 (Failed to load API data):', error)
        this.dataSource = 'error'

        // 回退到静态数据 (Fallback to static data)
        this.loadMockData()
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载模拟数据 (Load Mock Data)
     * 使用本地静态数据
     */
    loadMockData() {
      console.log('加载本地模拟数据 (Loading local mock data)...')

      // 使用本地静态数据 (Use local static data)
      this.pathData = yunnanLaosRoutes.map(route => ({
        id: route.id,
        coords: route.coords,
        value: route.value,
        name: route.name,
        type: route.type,
        metadata: route.metadata
      }))

      this.locationData = getAllLocations()
      this.dataSource = 'mock'

      console.log(`模拟数据加载成功 (Mock data loaded successfully): ${this.pathData.length} 条路径 (routes)`)
    },

    /**
     * 清除数据 (Clear Data)
     */
    clearData() {
      this.pathData = []
      this.locationData = []
      this.dataSource = 'none'
      console.log('数据已清除 (Data cleared)')
    },

    /**
     * 刷新数据 (Refresh Data)
     */
    async refreshData() {
      if (this.dataSource === 'api') {
        await this.loadApiData()
      } else if (this.dataSource === 'mock') {
        this.loadMockData()
      }
    },

    /**
     * 设置区域视图 (Set Regional View)
     */
    setRegionalView() {
      this.regionalMode = true
      this.mapMode = 'regional'
      this.regionConfig = {
        name: 'yunnan-laos',
        center: yunnanLaosMapConfig.center,
        zoom: yunnanLaosMapConfig.zoom,
        bounds: yunnanLaosMapConfig.bounds
      }
      console.log('切换到区域视图 (Switched to regional view)')
    },

    /**
     * 设置全球视图 (Set Global View)
     */
    setGlobalView() {
      this.regionalMode = false
      this.mapMode = 'global'
      console.log('切换到全球视图 (Switched to global view)')
    },

    /**
     * 聚焦云南省 (Focus on Yunnan Province)
     */
    focusOnYunnan() {
      this.regionalMode = true
      this.mapMode = 'yunnan-focus'
      this.regionConfig = {
        name: 'yunnan',
        center: [25.0, 101.0], // 云南省中心 (Yunnan Province center)
        zoom: 7,
        bounds: {
          north: 29.0,
          south: 21.0,
          east: 106.0,
          west: 97.0
        }
      }
      console.log('聚焦云南省 (Focused on Yunnan Province)')
    },

    /**
     * 聚焦老挝 (Focus on Laos)
     */
    focusOnLaos() {
      this.regionalMode = true
      this.mapMode = 'laos-focus'
      this.regionConfig = {
        name: 'laos',
        center: [18.0, 103.0], // 老挝中心 (Laos center)
        zoom: 7,
        bounds: {
          north: 22.5,
          south: 14.0,
          east: 108.0,
          west: 100.0
        }
      }
      console.log('聚焦老挝 (Focused on Laos)')
    },

    /**
     * 切换动画状态 (Toggle Animation)
     */
    toggleAnimation() {
      this.animationEnabled = !this.animationEnabled
      console.log(`动画${this.animationEnabled ? '开启' : '关闭'} (Animation ${this.animationEnabled ? 'enabled' : 'disabled'})`)
    },

    /**
     * 地图就绪回调 (Map Ready Callback)
     */
    onMapReady(map) {
      this.mapReady = true
      console.log('云南-老挝区域地图就绪 (Yunnan-Laos regional map ready):', map)
      console.log('地图中心点 (Map center):', map.getCenter())
      console.log('地图缩放级别 (Map zoom level):', map.getZoom())
    }
  },

  /**
   * 组件挂载后自动加载数据 (Auto load data after component mounted)
   */
  mounted() {
    console.log('云南-老挝地图测试组件已挂载 (Yunnan-Laos map test component mounted)')

    // 延迟加载数据以演示动态加载效果 (Delay loading data to demonstrate dynamic loading effect)
    setTimeout(() => {
      this.loadApiData()
    }, 1000)
  }
}
</script>

<style scoped>
/* 云南-老挝地图测试页面样式 (Yunnan-Laos Map Test Page Styles) */
.yunnan-laos-map-test {
  padding: 20px;
  background-color: #0F2E2C;
  color: #fff;
  min-height: 100vh;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

/* 页面标题样式 (Page Header Styles) */
.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(0, 255, 204, 0.1), rgba(0, 255, 204, 0.05));
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 204, 0.3);
}

.test-header h1 {
  color: #00FFCC;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: 600;
}

.test-header p {
  color: #ccc;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

/* 内容容器 (Content Container) */
.test-content {
  max-width: 1600px;
  margin: 0 auto;
}

/* 控制面板样式 (Control Panel Styles) */
.controls-section {
  margin-bottom: 30px;
  padding: 25px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 204, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.controls-section h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 20px;
  border-bottom: 2px solid rgba(0, 255, 204, 0.3);
  padding-bottom: 10px;
}

/* 控制组样式 (Control Group Styles) */
.control-group {
  margin-bottom: 25px;
  padding: 15px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.1);
}

.control-group h3 {
  color: #00FFCC;
  margin-bottom: 15px;
  font-size: 16px;
}

/* 按钮样式 (Button Styles) */
.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #00FFCC;
  color: #0F2E2C;
}

.btn-primary:hover:not(:disabled) {
  background-color: #00E6B8;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: rgba(0, 255, 204, 0.1);
  color: #00FFCC;
  border: 1px solid #00FFCC;
}

.btn-secondary:hover:not(:disabled) {
  background-color: rgba(0, 255, 204, 0.2);
}

.btn-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid #ffc107;
}

.btn-warning:hover:not(:disabled) {
  background-color: rgba(255, 193, 7, 0.2);
}

.btn-accent {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.btn-accent:hover:not(:disabled) {
  background-color: rgba(255, 107, 107, 0.2);
}

.btn-toggle {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.btn-toggle:hover:not(:disabled) {
  background-color: rgba(76, 175, 80, 0.2);
}

/* 动画控制样式 (Animation Control Styles) */
.animation-controls {
  margin: 20px 0;
  padding: 20px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.animation-controls h3 {
  color: #00FFCC;
  margin-bottom: 15px;
  font-size: 16px;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.control-row label {
  min-width: 140px;
  color: #ccc;
  font-size: 14px;
  font-weight: 500;
}

.slider {
  flex: 1;
  height: 6px;
  background: rgba(0, 255, 204, 0.2);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #00FFCC;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #00FFCC;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-row span {
  min-width: 70px;
  color: #00FFCC;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

/* 状态信息样式 (Status Information Styles) */
.status-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(0, 255, 204, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.status-item .label {
  color: #ccc;
  font-size: 14px;
}

.status-item .value {
  color: #00FFCC;
  font-size: 14px;
  font-weight: 600;
}

/* 地图区域样式 (Map Section Styles) */
.map-section {
  margin-bottom: 30px;
}

.map-section h2 {
  color: #00FFCC;
  margin-bottom: 15px;
  font-size: 20px;
}

/* 数据详情样式 (Data Details Styles) */
.data-details-section {
  margin-bottom: 30px;
  padding: 25px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.data-details-section h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 20px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.detail-card {
  padding: 20px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.1);
}

.detail-card h3 {
  color: #00FFCC;
  margin-bottom: 15px;
  font-size: 16px;
}

/* 位置和路径列表样式 (Location and Route List Styles) */
.location-list,
.route-list {
  max-height: 300px;
  overflow-y: auto;
}

.location-item,
.route-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 10px;
  margin-bottom: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 204, 0.1);
}

.location-name,
.route-name {
  color: #00FFCC;
  font-weight: 600;
  font-size: 14px;
}

.location-coords,
.location-type,
.route-value,
.route-type {
  color: #ccc;
  font-size: 12px;
}

/* 特性展示样式 (Features Section Styles) */
.features-section {
  padding: 25px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.features-section h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 20px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background-color: rgba(0, 255, 204, 0.1);
  transform: translateY(-2px);
}

.feature-item h3 {
  color: #00FFCC;
  margin-bottom: 10px;
  font-size: 16px;
}

.feature-item p {
  color: #ccc;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 (Responsive Design) */
@media (max-width: 768px) {
  .yunnan-laos-map-test {
    padding: 15px;
  }

  .test-header h1 {
    font-size: 24px;
  }

  .control-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .details-grid,
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .status-info {
    grid-template-columns: 1fr;
  }
}
</style>
