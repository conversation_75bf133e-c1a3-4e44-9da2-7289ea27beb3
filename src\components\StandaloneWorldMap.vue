<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div :id="mapId" class="map-container" :style="mapContainerStyle"></div>
  </div>
</template>

<script>
export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },
    // 区域模式配置 (Regional mode configuration)
    regionalMode: {
      type: Boolean,
      default: false
    },
    // 区域配置对象 (Regional configuration object)
    regionConfig: {
      type: Object,
      default: () => ({
        name: 'global',
        center: [20, 0],
        zoom: 2,
        bounds: null
      })
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },
    flowAnimationSpeed: {
      type: Number,
      default: 0.008, // Speed of flow particles along paths
      validator: (value) => value > 0 && value <= 0.1
    },
    flowParticleCount: {
      type: Number,
      default: 3, // Number of flow particles per path
      validator: (value) => value >= 1 && value <= 10
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    },

    // 最小化模式 - 隐藏文本标签和边界 (Minimalist mode - hide text labels and borders)
    minimalistMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      flowAnimations: [], // Store flow animation data
      leafletLoaded: false,
      svgObserver: null,
      mapId: `world-map-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    },
    // 计算实际使用的地图中心点 (Calculate actual map center to use)
    actualMapCenter() {
      return this.regionalMode && this.regionConfig.center
        ? this.regionConfig.center
        : this.center
    },
    // 计算实际使用的缩放级别 (Calculate actual zoom level to use)
    actualMapZoom() {
      return this.regionalMode && this.regionConfig.zoom
        ? this.regionConfig.zoom
        : this.zoom
    }
  },
  mounted() {
    this.loadLeaflet()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.leafletLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    },
    animationEnabled: {
      handler(newValue) {
        if (!newValue) {
          // Stop all flow animations when animation is disabled
          this.flowAnimations.forEach(animationData => {
            if (animationData.animationFrameId) {
              cancelAnimationFrame(animationData.animationFrameId)
              animationData.animationFrameId = null
            }
            animationData.isActive = false
          })
        } else if (this.map && this.leafletLoaded) {
          // Restart animations when re-enabled
          this.renderPaths()
        }
      }
    },
    // 监听区域模式变化 (Watch for regional mode changes)
    regionalMode: {
      handler() {
        if (this.map && this.leafletLoaded) {
          // 重新设置地图视图 (Reset map view)
          this.updateMapView()
        }
      }
    },

    // 监听颜色变化 (Watch for color changes)
    colors: {
      handler(newColors) {
        if (this.map && this.leafletLoaded) {
          console.log('颜色更新检测到 (Color update detected):', newColors)
          // 重新应用自定义样式 (Reapply custom styling)
          this.applyCustomStyling()
          // 重新渲染路径以应用新颜色 (Re-render paths to apply new colors)
          this.renderPaths()
        }
      },
      deep: true
    },

    // 监听简洁模式变化 (Watch for minimalist mode changes)
    minimalistMode: {
      handler() {
        if (this.map && this.leafletLoaded) {
          console.log('简洁模式切换 (Minimalist mode toggled):', this.minimalistMode)
          // 重新应用自定义样式 (Reapply custom styling)
          this.applyCustomStyling()
        }
      }
    },
    // 监听区域配置变化 (Watch for region config changes)
    regionConfig: {
      handler() {
        if (this.map && this.leafletLoaded && this.regionalMode) {
          // 更新地图视图 (Update map view)
          this.updateMapView()
        }
      },
      deep: true
    }
  },
  methods: {
    loadLeaflet() {
      // Check if Leaflet is already loaded
      if (window.L) {
        this.leafletLoaded = true
        this.initMap()
        return
      }

      // Dynamically load Leaflet CSS
      if (!document.querySelector('link[href*="leaflet.css"]')) {
        const leafletCss = document.createElement('link')
        leafletCss.rel = 'stylesheet'
        leafletCss.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
        document.head.appendChild(leafletCss)
      }

      // Dynamically load Leaflet JS
      if (!document.querySelector('script[src*="leaflet.js"]')) {
        const leafletScript = document.createElement('script')
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
        leafletScript.onload = () => {
          this.leafletLoaded = true
          this.initMap()
        }
        document.head.appendChild(leafletScript)
      }
    },

    initMap() {
      if (!this.leafletLoaded || !window.L) return

      // 使用计算后的中心点和缩放级别初始化地图 (Initialize map with computed center and zoom)
      // Initialize the map with fixed zoom and disabled interactions
      this.map = window.L.map(this.mapId, {
        center: this.actualMapCenter,
        zoom: this.actualMapZoom,
        zoomControl: false,
        attributionControl: false,
        dragging: false,
        touchZoom: false,
        doubleClickZoom: false,
        scrollWheelZoom: false,
        boxZoom: false,
        keyboard: false,
        tap: false
      })

      // 添加地图瓦片层 (Add map tile layer)
      if (this.minimalistMode) {
        // 使用CartoDB Positron瓦片 - 极简风格，最少标签 (Use CartoDB Positron tiles - minimalist style with minimal labels)
        window.L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {
          attribution: '&copy; OpenStreetMap &copy; CartoDB',
          subdomains: 'abcd',
          maxZoom: 18,
          opacity: 0.8
        }).addTo(this.map)
      } else {
        // 标准OpenStreetMap瓦片 (Standard OpenStreetMap tiles)
        window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map)
      }

      // Apply custom styling
      this.applyCustomStyling()

      // Setup SVG observer for path lines
      this.setupSVGObserver()

      // Render paths if data is available
      if (this.pathData.length > 0) {
        this.renderPaths()
      }

      // Emit ready event
      this.$emit('map-ready', this.map)
    },

    applyCustomStyling() {
      const styleId = `${this.mapId}-styles`

      // Remove existing styles if any
      const existingStyle = document.getElementById(styleId)
      if (existingStyle) {
        existingStyle.remove()
      }

      const styleElement = document.createElement('style')
      styleElement.id = styleId
      styleElement.textContent = `
        /* Map container background */
        #${this.mapId} {
          background-color: ${this.colors.ocean} !important;
        }
        #${this.mapId} .leaflet-container {
          background-color: ${this.colors.ocean} !important;
        }

        /*
         * 清洁地图样式 - 移除文本标签和边界线
         * Clean map styling - Remove text labels and border lines
         */
        ${this.minimalistMode ? `
        /*
         * 最小化模式样式 - 使用CartoDB无标签瓦片
         * Minimalist mode styling - using CartoDB no-labels tiles
         */
        #${this.mapId} .leaflet-tile-container img {
          filter:
            brightness(25%)
            sepia(60%)
            hue-rotate(120deg)
            contrast(200%)
            saturate(180%)
            invert(10%);
        }
        #${this.mapId} .leaflet-tile-container {
          opacity: 0.9;
        }

        /*
         * 深色主题覆盖层
         * Dark theme overlay
         */
        #${this.mapId} .leaflet-tile-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            ${this.colors.ocean} 0%,
            ${this.colors.land.replace('rgb(', 'rgba(').replace(')', ', 0.8)')} 50%,
            ${this.colors.ocean} 100%
          );
          mix-blend-mode: multiply;
          opacity: 0.7;
          pointer-events: none;
          z-index: 1;
        }

        /*
         * 地理轮廓增强 - 突出陆地形状
         * Geographic outline enhancement - highlight land shapes
         */
        #${this.mapId} .leaflet-tile-container::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background:
            radial-gradient(ellipse at 30% 40%, ${this.colors.land} 0%, transparent 60%),
            radial-gradient(ellipse at 70% 60%, ${this.colors.land} 0%, transparent 60%);
          mix-blend-mode: soft-light;
          opacity: 0.4;
          pointer-events: none;
          z-index: 2;
        }
        ` : `
        /* 标准模式样式 (Standard mode styling) */
        #${this.mapId} .leaflet-tile-container img {
          filter: brightness(40%) sepia(30%) hue-rotate(120deg) contrast(130%) saturate(120%);
        }
        #${this.mapId} .leaflet-tile-container {
          opacity: 0.9;
        }
        `}

        /*
         * 移除所有边界线和路径 (Remove all border lines and paths)
         */
        #${this.mapId} .leaflet-container path:not(.path-line):not(.source-point):not(.target-point):not(.flow-particle) {
          stroke: none !important;
          stroke-width: 0 !important;
          fill: none !important;
          opacity: 0 !important;
          display: none !important;
        }

        /* Ensure path connections have no fill */
        #${this.mapId} .leaflet-overlay-pane path.leaflet-interactive {
          fill-opacity: 0 !important;
        }

        /* Path line styling */
        #${this.mapId} .path-line {
          stroke: ${this.colors.pathLine} !important;
          stroke-width: 2px !important;
          fill: none !important;
          fill-opacity: 0 !important;
          filter: drop-shadow(0 0 3px ${this.colors.pathLine});
        }

        /* Source and target point styling */
        #${this.mapId} .source-point,
        #${this.mapId} .target-point {
          fill: ${this.colors.sourcePoint} !important;
          stroke: ${this.colors.sourcePoint} !important;
          stroke-width: 2px !important;
          filter: drop-shadow(0 0 5px ${this.colors.sourcePoint});
        }

        /* Flow particle styling */
        #${this.mapId} .flow-particle {
          fill: ${this.colors.pathLine} !important;
          stroke: ${this.colors.pathLine} !important;
          stroke-width: 1px !important;
          filter: drop-shadow(0 0 4px ${this.colors.pathLine});
          opacity: 0.9;
        }

        /* Grid overlay */
        ${this.showGrid ? `
        #${this.mapId}::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
          background-size: 20px 20px;
          pointer-events: none;
          z-index: 10;
        }
        ` : ''}

        /* Glow effect */
        #${this.mapId}::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
          pointer-events: none;
          z-index: 11;
        }
      `
      document.head.appendChild(styleElement)
    },

    setupSVGObserver() {
      // Create a MutationObserver to ensure path lines have no fill
      this.svgObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Find all path elements that might be path lines
                const paths = node.querySelectorAll ? node.querySelectorAll('path.leaflet-interactive') : []
                paths.forEach((path) => {
                  if (path.classList.contains('pure-line-path')) {
                    path.style.fill = 'none'
                    path.style.fillOpacity = '0'
                  }
                })
              }
            })
          }
        })
      })

      // Start observing the map container
      const mapContainer = document.getElementById(this.mapId)
      if (mapContainer) {
        this.svgObserver.observe(mapContainer, {
          childList: true,
          subtree: true
        })
      }
    },

    renderPaths() {
      if (!this.map || !window.L) return

      // Clear existing paths
      this.clearMapLayers()

      // Render each path
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) return

      const [sourceCoords, targetCoords] = path.coords

      // Create curved path using multiple points to simulate a curve
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)
      const pathLine = window.L.polyline(curvePoints, {
        color: this.colors.pathLine,
        weight: 2,
        opacity: 0.8,
        className: 'path-line pure-line-path'
      }).addTo(this.map)

      // Add tooltip to path line
      if (path.name) {
        pathLine.bindTooltip(path.name)
      }

      this.pathLines.push(pathLine)

      // Create source marker
      const sourceMarker = window.L.circleMarker(sourceCoords, {
        radius: Math.max(4, (path.value || 50) / 60),
        className: 'source-point',
        color: this.colors.sourcePoint,
        fillColor: this.colors.sourcePoint,
        fillOpacity: 0.8
      }).addTo(this.map)

      if (path.name) {
        const sourceName = path.name.split(' to ')[0] || 'Source'
        sourceMarker.bindTooltip(sourceName)
      }

      this.sourceMarkers.push(sourceMarker)

      // Create target marker
      const targetMarker = window.L.circleMarker(targetCoords, {
        radius: Math.max(4, (path.value || 50) / 60),
        className: 'target-point',
        color: this.colors.targetPoint,
        fillColor: this.colors.targetPoint,
        fillOpacity: 0.8
      }).addTo(this.map)

      if (path.name) {
        const targetName = path.name.split(' to ')[1] || 'Target'
        targetMarker.bindTooltip(targetName)
      }

      this.targetMarkers.push(targetMarker)

      // Add animation if enabled
      if (this.animationEnabled) {
        this.animateMarker(sourceMarker, index)
        this.animateMarker(targetMarker, index + 0.5) // Slight delay offset for target

        // Add flow animation along the path
        this.createFlowAnimation(curvePoints, index)
      }
    },

    generateCurvePoints(start, end, numPoints = 20) {
      const points = []

      // Calculate the midpoint
      const midLat = (start[0] + end[0]) / 2
      const midLng = (start[1] + end[1]) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2)
      )

      // Add curvature based on distance
      const curvature = distance * 0.3

      // Determine if we should curve up or down based on hemisphere
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * start[0] + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * end[0]
        const lng = Math.pow(1 - t, 2) * start[1] + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * end[1]
        points.push([lat, lng])
      }

      return points
    },

    animateMarker(marker, index) {
      if (!marker || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.015
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.015
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        // Update marker opacity using Leaflet's setStyle method
        try {
          marker.setStyle({
            fillOpacity: opacity,
            opacity: Math.min(opacity + 0.3, 1), // Higher border opacity for better glow effect
            weight: 2
          })
        } catch (e) {
          // Fallback to direct DOM manipulation if setStyle fails
          if (marker._path) {
            marker._path.style.fillOpacity = opacity
            marker._path.style.strokeOpacity = Math.min(opacity + 0.3, 1)
            marker._path.style.strokeWidth = '2px'
          }
        }

        // Continue animation if marker is still valid and animation is enabled
        if (marker._map && this.animationEnabled) {
          marker.animationFrameId = requestAnimationFrame(animate)
        }
      }

      // Start animation with a delay based on index
      setTimeout(() => {
        if (this.animationEnabled && marker._map) { // Check if marker is still on map
          marker.animationFrameId = requestAnimationFrame(animate)
        }
      }, index * 150) // Slightly faster stagger for better visual effect
    },

    createFlowAnimation(curvePoints, pathIndex) {
      if (!this.map || !window.L || !this.animationEnabled || curvePoints.length < 2) return

      // Create multiple flow particles for better visual effect
      const numParticles = this.flowParticleCount
      const particles = []

      for (let i = 0; i < numParticles; i++) {
        // Create a small circle marker for the flow particle
        const particle = window.L.circleMarker([0, 0], {
          radius: 3,
          className: 'flow-particle',
          color: this.colors.pathLine,
          fillColor: this.colors.pathLine,
          fillOpacity: 0.9,
          weight: 1
        }).addTo(this.map)

        particles.push({
          marker: particle,
          progress: (i / numParticles), // Stagger particles along the path
          pathIndex: pathIndex
        })
      }

      // Animation state
      const animationData = {
        particles: particles,
        curvePoints: curvePoints,
        pathIndex: pathIndex,
        animationFrameId: null,
        isActive: true
      }

      this.flowAnimations.push(animationData)

      // Animation function
      const animateFlow = () => {
        if (!this.animationEnabled || !animationData.isActive) {
          return
        }

        let allParticlesValid = true

        animationData.particles.forEach((particle) => {
          if (!particle.marker._map) {
            allParticlesValid = false
            return
          }

          // Update particle progress
          particle.progress += this.flowAnimationSpeed

          // Reset progress when particle reaches the end
          if (particle.progress >= 1) {
            particle.progress = 0
          }

          // Calculate position along the curve
          const position = this.getPositionAlongPath(curvePoints, particle.progress)
          if (position) {
            try {
              particle.marker.setLatLng(position)

              // Add fade effect based on progress
              const opacity = Math.sin(particle.progress * Math.PI) * 0.7 + 0.3
              particle.marker.setStyle({
                fillOpacity: opacity,
                opacity: opacity
              })
            } catch (e) {
              // Handle potential errors gracefully
              console.warn('Flow animation error:', e)
              allParticlesValid = false
            }
          }
        })

        // Continue animation if all particles are valid and animation is enabled
        if (allParticlesValid && this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        } else {
          // Clean up if animation should stop
          this.cleanupFlowAnimation(animationData)
        }
      }

      // Start the animation with a delay based on path index
      setTimeout(() => {
        if (this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        }
      }, pathIndex * 200)
    },

    getPositionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // Clamp progress to valid range
      progress = Math.max(0, Math.min(1, progress))

      // Calculate the exact position along the path
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // Handle edge case where progress is exactly 1
      if (segmentIndex >= totalSegments) {
        return points[points.length - 1]
      }

      // Interpolate between two points
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const lat = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lng = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lat, lng]
    },

    cleanupFlowAnimation(animationData) {
      if (!animationData) return

      // Cancel animation frame
      if (animationData.animationFrameId) {
        cancelAnimationFrame(animationData.animationFrameId)
        animationData.animationFrameId = null
      }

      // Remove particles from map
      animationData.particles.forEach((particle) => {
        if (particle.marker && this.map) {
          try {
            this.map.removeLayer(particle.marker)
          } catch (e) {
            // Ignore errors during cleanup
          }
        }
      })

      // Mark as inactive
      animationData.isActive = false
    },

    clearMapLayers() {
      // Clear path lines
      this.pathLines.forEach(line => {
        if (this.map && line) {
          this.map.removeLayer(line)
        }
      })
      this.pathLines = []

      // Clear source markers
      this.sourceMarkers.forEach(marker => {
        // Clear animation frames
        if (marker.animationFrameId) {
          cancelAnimationFrame(marker.animationFrameId)
        }
        // Clear old interval IDs (backward compatibility)
        if (marker.intervalId) {
          clearInterval(marker.intervalId)
        }
        if (this.map && marker) {
          this.map.removeLayer(marker)
        }
      })
      this.sourceMarkers = []

      // Clear target markers
      this.targetMarkers.forEach(marker => {
        // Clear animation frames
        if (marker.animationFrameId) {
          cancelAnimationFrame(marker.animationFrameId)
        }
        // Clear old interval IDs (backward compatibility)
        if (marker.intervalId) {
          clearInterval(marker.intervalId)
        }
        if (this.map && marker) {
          this.map.removeLayer(marker)
        }
      })
      this.targetMarkers = []

      // Clear flow animations
      this.flowAnimations.forEach(animationData => {
        this.cleanupFlowAnimation(animationData)
      })
      this.flowAnimations = []
    },

    cleanup() {
      // Clear all layers
      this.clearMapLayers()

      // Remove map
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // Disconnect SVG observer
      if (this.svgObserver) {
        this.svgObserver.disconnect()
        this.svgObserver = null
      }

      // Remove custom styles
      const styleElement = document.getElementById(`${this.mapId}-styles`)
      if (styleElement) {
        styleElement.remove()
      }
    },

    // Public methods for external control
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },

    updateColors(newColors) {
      Object.assign(this.colors, newColors)
      if (this.map) {
        this.applyCustomStyling()
        this.renderPaths()
      }
    },

    /**
     * 更新地图视图到新的中心点和缩放级别
     * Update map view to new center and zoom level
     */
    updateMapView() {
      if (!this.map) return

      try {
        // 设置新的地图视图 (Set new map view)
        this.map.setView(this.actualMapCenter, this.actualMapZoom, {
          animate: true,
          duration: 1.0 // 1秒动画过渡 (1 second animation transition)
        })

        // 如果有边界限制，设置最大边界 (If there are boundary restrictions, set max bounds)
        if (this.regionalMode && this.regionConfig.bounds) {
          const bounds = this.regionConfig.bounds
          const leafletBounds = window.L.latLngBounds(
            [bounds.south, bounds.west], // 西南角 (Southwest corner)
            [bounds.north, bounds.east]  // 东北角 (Northeast corner)
          )
          this.map.setMaxBounds(leafletBounds)
        } else {
          // 清除边界限制 (Clear boundary restrictions)
          this.map.setMaxBounds(null)
        }

        console.log(`地图视图已更新 (Map view updated): 中心点 (Center) ${this.actualMapCenter}, 缩放级别 (Zoom) ${this.actualMapZoom}`)
      } catch (error) {
        console.error('更新地图视图失败 (Failed to update map view):', error)
      }
    },

    /**
     * 设置区域模式
     * Set regional mode
     * @param {Object} regionConfig - 区域配置对象 (Regional configuration object)
     */
    setRegionalMode(regionConfig) {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', true)
      this.$emit('update:regionConfig', { ...this.regionConfig, ...regionConfig })
      this.updateMapView()
    },

    /**
     * 切换到全球模式
     * Switch to global mode
     */
    setGlobalMode() {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', false)
      this.updateMapView()
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  border-radius: 8px;
  overflow: hidden;
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}
</style>
