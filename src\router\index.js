import Vue from 'vue'
import VueRouter from 'vue-router'
import GlobalDistribution from '@/views/GlobalDistribution.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '@/components/HomeView.vue')
  },
  {
    path: '/global-distribution',
    name: 'GlobalDistribution',
    component: GlobalDistribution
  },
  {
    path: '/world-map-demo',
    name: 'WorldMapDemo',
    component: () => import(/* webpackChunkName: "demo" */ '@/components/WorldMapDemo.vue')
  },
  {
    path: '/standalone-map-test',
    name: 'StandaloneMapTest',
    component: () => import(/* webpackChunkName: "test" */ '@/views/StandaloneMapTest.vue')
  },
  {
    path: '/standalone-global-distribution',
    name: 'StandaloneGlobalDistribution',
    component: () => import(/* webpackChunkName: "standalone" */ '@/views/StandaloneGlobalDistribution.vue')
  },
  {
    path: '/yunnan-laos-map-test',
    name: 'YunnanLaosMapTest',
    component: () => import(/* webpackChunkName: "yunnan-laos" */ '@/views/YunnanLaosMapTest.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
